import { create } from 'zustand';
import { Teacher, Classroom, Level, TimeSlot } from '@/lib/schemas';

interface StoreState {
  teachers: Teacher[];
  classrooms: Classroom[];
  levels: Level[];
  timeSlots: TimeSlot[];
  selectedTeacher: string | null;
  selectedClassroom: string | null;
  
  // Actions
  setTeachers: (teachers: Teacher[]) => void;
  addTeacher: (teacher: Teacher) => void;
  updateTeacher: (id: string, teacher: Partial<Teacher>) => void;
  deleteTeacher: (id: string) => void;
  
  setClassrooms: (classrooms: Classroom[]) => void;
  addClassroom: (classroom: Classroom) => void;
  updateClassroom: (id: string, classroom: Partial<Classroom>) => void;
  deleteClassroom: (id: string) => void;
  
  setLevels: (levels: Level[]) => void;
  addLevel: (level: Level) => void;
  updateLevel: (id: string, level: Partial<Level>) => void;
  deleteLevel: (id: string) => void;
  
  setTimeSlots: (timeSlots: TimeSlot[]) => void;
  addTimeSlot: (timeSlot: TimeSlot) => void;
  updateTimeSlot: (id: string, timeSlot: Partial<TimeSlot>) => void;
  deleteTimeSlot: (id: string) => void;
  
  setSelectedTeacher: (teacherId: string | null) => void;
  setSelectedClassroom: (classroomId: string | null) => void;
  
  // Utilities
  getFilteredTimeSlots: () => TimeSlot[];
  checkConflict: (newSlot: Omit<TimeSlot, 'id'>) => boolean;
}

export const useStore = create<StoreState>((set, get) => ({
  teachers: [
    { id: '1', name: 'Sarah Johnson', color: '#FF6B6B' },
    { id: '2', name: 'Michael Chen', color: '#4ECDC4' },
    { id: '3', name: 'Emily Rodriguez', color: '#45B7D1' },
    { id: '4', name: 'David Kim', color: '#96CEB4' },
  ],
  classrooms: [
    { id: '1', name: 'Room A1', capacity: 20 },
    { id: '2', name: 'Room B2', capacity: 15 },
    { id: '3', name: 'Room C3', capacity: 25 },
    { id: '4', name: 'Lab D1', capacity: 12 },
  ],
  levels: [
    { id: '1', label: 'Beginner', color: '#FF9F43' },
    { id: '2', label: 'Intermediate', color: '#6C5CE7' },
    { id: '3', label: 'Advanced', color: '#A29BFE' },
    { id: '4', label: 'Business', color: '#FD79A8' },
  ],
  timeSlots: [],
  selectedTeacher: null,
  selectedClassroom: null,

  setTeachers: (teachers) => set({ teachers }),
  addTeacher: (teacher) => set((state) => ({ teachers: [...state.teachers, teacher] })),
  updateTeacher: (id, updates) =>
    set((state) => ({
      teachers: state.teachers.map((t) => (t.id === id ? { ...t, ...updates } : t)),
    })),
  deleteTeacher: (id) =>
    set((state) => ({
      teachers: state.teachers.filter((t) => t.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.teacherId !== id),
    })),

  setClassrooms: (classrooms) => set({ classrooms }),
  addClassroom: (classroom) => set((state) => ({ classrooms: [...state.classrooms, classroom] })),
  updateClassroom: (id, updates) =>
    set((state) => ({
      classrooms: state.classrooms.map((c) => (c.id === id ? { ...c, ...updates } : c)),
    })),
  deleteClassroom: (id) =>
    set((state) => ({
      classrooms: state.classrooms.filter((c) => c.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.classroomId !== id),
    })),

  setLevels: (levels) => set({ levels }),
  addLevel: (level) => set((state) => ({ levels: [...state.levels, level] })),
  updateLevel: (id, updates) =>
    set((state) => ({
      levels: state.levels.map((l) => (l.id === id ? { ...l, ...updates } : l)),
    })),
  deleteLevel: (id) =>
    set((state) => ({
      levels: state.levels.filter((l) => l.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.levelId !== id),
    })),

  setTimeSlots: (timeSlots) => set({ timeSlots }),
  addTimeSlot: (timeSlot) => set((state) => ({ timeSlots: [...state.timeSlots, timeSlot] })),
  updateTimeSlot: (id, updates) =>
    set((state) => ({
      timeSlots: state.timeSlots.map((ts) => (ts.id === id ? { ...ts, ...updates } : ts)),
    })),
  deleteTimeSlot: (id) =>
    set((state) => ({
      timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
    })),

  setSelectedTeacher: (teacherId) => set({ selectedTeacher: teacherId }),
  setSelectedClassroom: (classroomId) => set({ selectedClassroom: classroomId }),

  getFilteredTimeSlots: () => {
    const { timeSlots, selectedTeacher, selectedClassroom } = get();
    return timeSlots.filter((slot) => {
      if (selectedTeacher && slot.teacherId !== selectedTeacher) return false;
      if (selectedClassroom && slot.classroomId !== selectedClassroom) return false;
      return true;
    });
  },

  checkConflict: (newSlot) => {
    const { timeSlots } = get();
    return timeSlots.some((slot) => {
      if (slot.dayOfWeek !== newSlot.dayOfWeek) return false;
      
      const slotStart = new Date(`2024-01-01 ${slot.start}`);
      const slotEnd = new Date(`2024-01-01 ${slot.end}`);
      const newStart = new Date(`2024-01-01 ${newSlot.start}`);
      const newEnd = new Date(`2024-01-01 ${newSlot.end}`);
      
      const timeOverlap = newStart < slotEnd && newEnd > slotStart;
      
      if (!timeOverlap) return false;
      
      // Only conflict if same teacher OR same classroom is being used
      return slot.teacherId === newSlot.teacherId || slot.classroomId === newSlot.classroomId;
    });
  },
}));
