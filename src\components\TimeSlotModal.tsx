
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { TimeSlot, TimeSlotSchema } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';

interface TimeSlotModalProps {
  isOpen: boolean;
  onClose: () => void;
  existingSlot?: TimeSlot | null;
  defaultDay?: number;
  defaultTime?: string;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const TimeSlotModal: React.FC<TimeSlotModalProps> = ({
  isOpen,
  onClose,
  existingSlot,
  defaultDay,
  defaultTime,
}) => {
  const { teachers, classrooms, levels, addTimeSlot, updateTimeSlot, checkConflict } = useStore();
  
  const [formData, setFormData] = useState({
    teacherId: '',
    classroomId: '',
    levelId: '',
    start: '',
    end: '',
    dayOfWeek: 0,
  });

  useEffect(() => {
    if (existingSlot) {
      setFormData({
        teacherId: existingSlot.teacherId,
        classroomId: existingSlot.classroomId,
        levelId: existingSlot.levelId,
        start: existingSlot.start,
        end: existingSlot.end,
        dayOfWeek: existingSlot.dayOfWeek,
      });
    } else if (defaultDay !== undefined && defaultTime) {
      setFormData({
        teacherId: '',
        classroomId: '',
        levelId: '',
        start: defaultTime,
        end: '',
        dayOfWeek: defaultDay,
      });
    }
  }, [existingSlot, defaultDay, defaultTime]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const newSlot = {
        id: existingSlot?.id || Date.now().toString(),
        ...formData,
      };

      TimeSlotSchema.parse(newSlot);

      // Only check conflict for new slots (not when editing existing ones)
      if (!existingSlot && checkConflict(formData)) {
        toast({
          title: "Conflict Detected",
          description: "This teacher or classroom is already scheduled at this time. Choose a different teacher/classroom or time slot.",
          variant: "destructive",
        });
        return;
      }

      if (existingSlot) {
        updateTimeSlot(existingSlot.id, formData);
        toast({
          title: "Time Slot Updated",
          description: "The time slot has been successfully updated.",
        });
      } else {
        addTimeSlot(newSlot);
        toast({
          title: "Time Slot Created",
          description: "The time slot has been successfully created.",
        });
      }

      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Please fill in all required fields correctly.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-card border-white/20">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {existingSlot ? 'Edit Time Slot' : 'Create Time Slot'}
          </DialogTitle>
        </DialogHeader>

        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="teacher">Teacher</Label>
            <Select
              value={formData.teacherId}
              onValueChange={(value) => setFormData({ ...formData, teacherId: value })}
            >
              <SelectTrigger className="glass-button">
                <SelectValue placeholder="Select a teacher" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: teacher.color }}
                      />
                      <span>{teacher.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="classroom">Classroom</Label>
            <Select
              value={formData.classroomId}
              onValueChange={(value) => setFormData({ ...formData, classroomId: value })}
            >
              <SelectTrigger className="glass-button">
                <SelectValue placeholder="Select a classroom" />
              </SelectTrigger>
              <SelectContent>
                {classrooms.map((classroom) => (
                  <SelectItem key={classroom.id} value={classroom.id}>
                    {classroom.name} (Capacity: {classroom.capacity})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="level">Level</Label>
            <Select
              value={formData.levelId}
              onValueChange={(value) => setFormData({ ...formData, levelId: value })}
            >
              <SelectTrigger className="glass-button">
                <SelectValue placeholder="Select a level" />
              </SelectTrigger>
              <SelectContent>
                {levels.map((level) => (
                  <SelectItem key={level.id} value={level.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: level.color }}
                      />
                      <span>{level.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="day">Day of Week</Label>
            <Select
              value={formData.dayOfWeek.toString()}
              onValueChange={(value) => setFormData({ ...formData, dayOfWeek: parseInt(value) })}
            >
              <SelectTrigger className="glass-button">
                <SelectValue placeholder="Select a day" />
              </SelectTrigger>
              <SelectContent>
                {DAYS.map((day, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {day}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start">Start Time</Label>
              <Input
                id="start"
                type="time"
                value={formData.start}
                onChange={(e) => setFormData({ ...formData, start: e.target.value })}
                className="glass-button"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end">End Time</Label>
              <Input
                id="end"
                type="time"
                value={formData.end}
                onChange={(e) => setFormData({ ...formData, end: e.target.value })}
                className="glass-button"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} className="glass-button">
              Cancel
            </Button>
            <Button type="submit" className="glass-button bg-primary/20 hover:bg-primary/30">
              {existingSlot ? 'Update' : 'Create'}
            </Button>
          </div>
        </motion.form>
      </DialogContent>
    </Dialog>
  );
};
