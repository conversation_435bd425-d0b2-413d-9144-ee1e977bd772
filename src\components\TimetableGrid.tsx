import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { TimeSlot } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, X, Edit } from 'lucide-react';
import { TimeSlotModal } from './TimeSlotModal';

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const TIME_SLOTS = Array.from({ length: 24 }, (_, i) => {
  const hour = Math.floor(i / 2) + 8;
  const minute = i % 2 === 0 ? '00' : '30';
  return `${hour.toString().padStart(2, '0')}:${minute}`;
});

export const TimetableGrid: React.FC = () => {
  const {
    getFilteredTimeSlots,
    teachers,
    classrooms,
    levels,
    deleteTimeSlot,
    selectedTeacher,
    selectedClassroom,
  } = useStore();
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [selectedCell, setSelectedCell] = useState<{ day: number; time: string } | null>(null);

  const timeSlots = getFilteredTimeSlots();

  const getSlotsForCell = (day: number, time: string) => {
    return timeSlots.filter((slot) => {
      const slotStartTime = slot.start;
      const slotEndTime = slot.end;
      return slot.dayOfWeek === day && time >= slotStartTime && time < slotEndTime;
    });
  };

  const handleCellClick = (day: number, time: string, existingSlot?: TimeSlot) => {
    if (existingSlot) {
      setSelectedSlot(existingSlot);
      setSelectedCell(null);
    } else {
      setSelectedCell({ day, time });
      setSelectedSlot(null);
    }
    setIsModalOpen(true);
  };

  const handleDeleteSlot = (slotId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    deleteTimeSlot(slotId);
  };

  const getTeacherById = (id: string) => teachers.find(t => t.id === id);
  const getClassroomById = (id: string) => classrooms.find(c => c.id === id);
  const getLevelById = (id: string) => levels.find(l => l.id === id);

  const renderTimeSlot = (slot: TimeSlot, isStacked: boolean = false) => {
    const teacher = getTeacherById(slot.teacherId);
    const classroom = getClassroomById(slot.classroomId);
    const level = getLevelById(slot.levelId);

    if (!teacher || !classroom || !level) return null;

    return (
      <motion.div
        key={slot.id}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className={`glass-card p-2 ${isStacked ? 'mb-1' : 'm-1'} relative group cursor-pointer shine-effect`}
        style={{ backgroundColor: `${teacher.color}20`, borderColor: teacher.color }}
        onClick={() => handleCellClick(slot.dayOfWeek, slot.start, slot)}
      >
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-1 right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => handleDeleteSlot(slot.id, e)}
        >
          <X className="h-3 w-3" />
        </Button>
        
        <div className="text-xs font-medium text-gray-900 mb-1">{teacher.name}</div>
        <div className="text-xs text-gray-700 mb-1">{classroom.name}</div>
        <Badge 
          variant="secondary" 
          className="text-xs"
          style={{ backgroundColor: level.color, color: 'white' }}
        >
          {level.label}
        </Badge>
        {!isStacked && (
          <div className="text-xs text-gray-600 mt-1">
            {slot.start} - {slot.end}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className="glass-card p-6 liquid-container">
      <div className="liquid-background absolute inset-0 opacity-30"></div>
      
      <div className="relative z-10">
        <div className="grid grid-cols-8 gap-2 mb-4">
          <div className="font-semibold text-center p-2"></div>
          {DAYS.map((day, index) => (
            <div key={day} className="font-semibold text-center p-2 glass-card">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-8 gap-2 max-h-[600px] overflow-y-auto">
          {TIME_SLOTS.map((time) => (
            <React.Fragment key={time}>
              <div className="text-sm text-center p-2 glass-card font-medium">
                {time}
              </div>
              {DAYS.map((_, dayIndex) => {
                const slots = getSlotsForCell(dayIndex, time);
                return (
                  <div
                    key={`${dayIndex}-${time}`}
                    className="min-h-[60px] border border-gray-200 rounded-lg relative hover:bg-glass-light transition-all cursor-pointer"
                    onClick={() => handleCellClick(dayIndex, time)}
                  >
                    <AnimatePresence>
                      {slots.length > 0 ? (
                        <div className="h-full overflow-y-auto">
                          {slots.map((slot, index) => 
                            renderTimeSlot(slot, slots.length > 1)
                          )}
                        </div>
                      ) : (
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          className="w-full h-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
                        >
                          <Plus className="h-4 w-4 text-gray-400" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>

      <TimeSlotModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedSlot(null);
          setSelectedCell(null);
        }}
        existingSlot={selectedSlot}
        defaultDay={selectedCell?.day}
        defaultTime={selectedCell?.time}
      />
    </div>
  );
};
